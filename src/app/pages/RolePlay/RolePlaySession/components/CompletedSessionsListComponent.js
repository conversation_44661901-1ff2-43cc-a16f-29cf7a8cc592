import React from 'react';
import { List, Card, Typography, Tag, Spin, Empty, Space, Tooltip } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, EyeOutlined, BarChartOutlined, LineChartOutlined } from '@ant-design/icons';
import AntButton from "@component/AntButton";
import { BUTTON } from '@constant';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@src/constants/link';
import CheckCircle from "@src/asset/icon/check/CheckCircle.svg";
import ProgressCircle from '@src/app/component/SvgIcons/ProgressCircle';
import CheckGreenBoder from '@src/app/component/SvgIcons/Check/CheckGreenBorder';

const { Title, Text, Paragraph } = Typography;

const CompletedSessionsListComponent = ({ sessions, loading, courseId, scenarios }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Tính toán số lượng kịch bản duy nhất đã được hoàn thành
  const calculateCompletedScenariosCount = () => {
    if (!scenarios || !sessions || scenarios.length === 0 || sessions.length === 0) {
      return 0;
    }
    // Lấy danh sách aiScenarioId từ các sessions đã hoàn thành
    const completedSessionScenarioIds = sessions
      .filter(session => {
        // Session được coi là hoàn thành nếu điểm simulationScore >= 70
        const hasValidStatus = session.status === 'completed' || session.status === 'analyzed';
        const hasValidScore = session.analysisId?.result?.simulationScore >= (session.aiScenarioId?.passScore ?? 70);
        return hasValidStatus && hasValidScore;
      })
      .map(session => {
        return typeof session.aiScenarioId === 'object'
          ? session.aiScenarioId?._id || session.aiScenarioId?.id
          : session.aiScenarioId;
      })
      .filter(Boolean); // Loại bỏ giá trị null/undefined

    // Tìm các scenario duy nhất đã có ít nhất 1 session hoàn thành
    const uniqueCompletedScenarios = scenarios.filter(scenario => {
      const scenarioId = scenario._id || scenario.id;
      return completedSessionScenarioIds.includes(scenarioId);
    });

    return uniqueCompletedScenarios.length;
  };

  const progress = calculateCompletedScenariosCount();

  const handleViewDetails = (sessionId) => {
    if (courseId && sessionId) {
      navigate(LINK.ROLE_PLAY_SESSION_RESULT.format(courseId, sessionId));
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!sessions || sessions.length === 0) {
    return (
      <Card className="role-play-session-screen__session-list" title={<Title level={5}>{t('COMPLETED_SESSIONS_HISTORY_', 'Lịch sử luyện tập')}</Title>}>
        <Empty description={t('NO_COMPLETED_SESSIONS_', 'Bạn chưa có buổi luyện tập nào được hoàn thành cho khóa học này.')} />
      </Card>
    );
  }

  const getStatusTag = (session) => {
    if (session.status === "analyzed") {
      return (
        <Tag icon={<BarChartOutlined />} style={{backgroundColor:"#DDFFF2", color:"#17b177"}}>
          {t('ANALYZED_', 'Đã phân tích')}
        </Tag>
      );
    }
  };

  return (
    <div className='role-play-session-screen__session-list__wrapper'>
      <div className='role-play-session-screen__session-list__completed-status'>
        <div className='font-bold text-lg px-6'>
          {t("COMPLETION_STATUS_STB", "Tình trạng hoàn thành")}
        </div>
        <div className='h-[0.5px] w-auto bg-[#f0f0f0] '></div>
        <div className='flex items-center justify-center'>
          <ProgressCircle percent={scenarios?.length >= progress && scenarios?.length > 0 ? Math.round((progress / scenarios?.length) * 100) : '0'} />
        </div>
        <div className='font-[Segoe UI] flex flex-col justify-center items-center gap-1'>
          <>{t("SCENARIOS_COMPLETED_STB", "Kịch bản trò chuyện hoàn thành:")} </>
          <span className='font-semibold text-[#4d45be]'>
            {progress}/{scenarios?.length}
          </span>
        </div>
      </div>
      <Card className="role-play-session-screen__session-list" title={<Title level={5}>{t('COMPLETED_SESSIONS_HISTORY_', 'Lịch sử luyện tập')}</Title>}>
        <List
          itemLayout="vertical"
          dataSource={sessions}
          pagination={{
            pageSize: 5, // Show 5 items per page
            align: 'center',
            showLessItems: true,
            showSizeChanger: false,
          }}
          renderItem={(session) => (
            <List.Item
              key={session._id}
              onClick={() => handleViewDetails(session._id)} // click toàn bộ item
              style={{ cursor: 'pointer' }}
              actions={[]}
            >
              <List.Item.Meta
                title={session.aiScenarioId?.name || t('UNKNOWN_COURSE', 'Kịch bản không xác định')}
                description={
                  <Space direction="vertical" size="small">
                    <div className='flex gap-1 items-center'>
                      <img
                        src={CheckCircle}
                        alt="Check Circle"
                      />
                      <Text type="secondary">
                        {t('COMPLETED_ON_', 'Hoàn thành vào:')}{' '}
                        {session.endTime ? moment(session.endTime).format('HH:mm, DD/MM/YYYY') : t('N/A', 'N/A')}
                      </Text>
                    </div>
                    {session.status === "analyzed" && (
                      <div>
                        {getStatusTag(session)}
                        {session.analysisId?.result?.simulationScore !== undefined && (
                          <Tag icon={<CheckCircleOutlined />} style={{ marginLeft: '8px', backgroundColor:"#e4dffe", color:"#4d45be" }}>
                            {t('SCORE_STB', 'Điểm:')} {session.analysisId.result.simulationScore}
                          </Tag>
                        )}
                      </div>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default CompletedSessionsListComponent;
