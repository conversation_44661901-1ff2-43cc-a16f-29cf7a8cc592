import React from 'react';
import {<PERSON><PERSON>, Select, Switch, Button, Spin, Tooltip} from 'antd';
import {useTranslation} from 'react-i18next';
import AntButton from "@component/AntButton";
import {BUTTON, CONSTANT} from '@constant';

const MediaDeviceSelectionModal = ({
  open,
  onCancel,
  onOk,
  onRefreshDevices,
  enumeratingDevices,
  cameraEnabled,
  onCameraEnabledChange,
  selectedCamera,
  onCameraChange,
  selectedMic,
  onMicChange,
  availableCameras,
  availableMics,
  availableSpeakers,
  selectedSpeaker,
  onSpeakerChange,
  isSpeakerSelectionSupported,
}) => {
  const {t} = useTranslation();

  return (
    <Modal
      className='role-play-session-screen__device-selector-modal'
      title={t('SELECT_MEDIA_DEVICES_', 'Chọn thiết bị âm thanh và hình ảnh')}
      open={open}
      onCancel={onCancel}
      footer={[
        <AntButton
          key="submit"
          type={BUTTON.DEEP_NAVY2}
          onClick={onOk}
          disabled={!selectedMic} // Chỉ yêu cầu có mic, camera có thể tắt
          loading={enumeratingDevices}
          size="large"
        >
          {t('START_SESSION_', 'Bắt đầu phiên')}
        </AntButton>,
      ]}
    >
      <div className="role-play-session-screen__device-selector">
        {enumeratingDevices ? (
          <div style={{textAlign: 'center', padding: '20px'}}>
            <Spin />
            <p style={{marginTop: '10px'}}>{t('LOADING_DEVICES_', 'Đang tải danh sách thiết bị...')}</p>
          </div>
        ) : (
          <>
            <div className="role-play-session-screen__camera-option">
              <div
                style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px'}}
              >
                <span className='font-medium'>{t('ENABLE_CAMERA_', 'Bật camera')}</span>
                <Switch checked={cameraEnabled} onChange={onCameraEnabledChange} />
              </div>

              {cameraEnabled && (
                <>
                  <Select
                    style={{width: '100%', marginBottom: '10px'}}
                    placeholder={t('SELECT_CAMERA_', 'Chọn camera')}
                    value={selectedCamera}
                    onChange={onCameraChange}
                    disabled={!cameraEnabled || availableCameras.length === 0}
                    notFoundContent={t('NO_CAMERAS_', 'Không tìm thấy camera')}
                    showSearch
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.children?.toLowerCase() ?? '').includes(input.toLowerCase())
                    }
                  >
                    {availableCameras.map((camera, index) => {
                      let deviceName = camera.label || `Camera ${index + 1}`;
                      if (!camera.label) {
                        deviceName += ` (ID: ${camera.deviceId.substring(0, 8)}...)`;
                      }

                      return (
                        <Select.Option key={camera.deviceId} value={camera.deviceId}>
                          {deviceName}
                        </Select.Option>
                      );
                    })}
                  </Select>

                  {/* {availableCameras.length > 0 && selectedCamera && (
                    <div style={{marginBottom: '20px', fontSize: '12px', color: '#666'}}>
                      <p>{t('SELECTED_CAMERA_INFO', 'Thông tin camera đã chọn')}:</p>
                      <div style={{backgroundColor: '#f5f5f5', padding: '5px', borderRadius: '4px'}}>
                        {availableCameras.find(c => c.deviceId === selectedCamera)?.label || 'Không có thông tin label'}
                        <br />
                        ID: {selectedCamera}
                      </div>
                    </div>
                  )} */}

                  {availableCameras.length === 0 && (
                    <p style={{color: 'orange', margin: '0px'}}>
                      {t(
                        'NO_CAMERAS_FOUND',
                        'Không tìm thấy camera nào. Vui lòng kiểm tra lại quyền truy cập và thiết bị của bạn. Hoặc bạn có thể bắt đầu mà không cần dùng camera.',
                      )}
                    </p>
                  )}
                </>
              )}
            </div>

            {isSpeakerSelectionSupported && (
              <div className="role-play-session-screen__speaker-option">
                <p className="font-medium">{t('SELECT_SPEAKER', 'Chọn thiết bị phát')}</p>
                <Select
                  style={{width: '100%', marginBottom: '10px'}}
                  placeholder={t('SELECT_SPEAKER', 'Chọn thiết bị phát')}
                  value={selectedSpeaker}
                  onChange={onSpeakerChange}
                  disabled={availableSpeakers.length === 0}
                  notFoundContent={t('NO_SPEAKERS_FOUND', 'Không tìm thấy thiết bị phát')}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) => (option?.children?.toLowerCase() ?? '').includes(input.toLowerCase())}
                >
                  {availableSpeakers.map((speaker, index) => {
                    let deviceName = speaker.label || `Speaker ${index + 1}`;
                    if (speaker.deviceId === 'default') {
                      deviceName = `${t('DEFAULT_SPEAKER', 'Mặc định')} - ${speaker.label || ''}`;
                    }
                    if (!speaker.label) {
                      deviceName += ` (ID: ${speaker.deviceId.substring(0, 8)}...)`;
                    }
                    return (
                      <Select.Option key={speaker.deviceId} value={speaker.deviceId}>
                        {deviceName}
                      </Select.Option>
                    );
                  })}
                </Select>
                {availableSpeakers.length === 0 && (
                  <p style={{color: 'orange', margin: '0px'}}>
                    {t(
                      'NO_SPEAKERS_FOUND_NOTE',
                      'Không tìm thấy thiết bị phát nào. Âm thanh sẽ được phát qua thiết bị mặc định của hệ thống.',
                    )}
                  </p>
                )}
              </div>
            )}

            <div className="role-play-session-screen__mic-option">
              <p className='font-medium'>
                {t('SELECT_MICROPHONE_', 'Chọn microphone')} <span style={{color: 'red'}}>*</span>
              </p>
              <Select
                style={{width: '100%', marginBottom: '10px'}}
                placeholder={t('SELECT_MICROPHONE_', 'Chọn microphone')}
                value={selectedMic}
                onChange={onMicChange}
                disabled={availableMics.length === 0}
                notFoundContent={t('NO_MICROPHONES_', 'Không tìm thấy microphone')}
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) => (option?.children?.toLowerCase() ?? '').includes(input.toLowerCase())}
              >
                {availableMics.map((mic, index) => {
                  let deviceName = mic.label || `Microphone ${index + 1}`;
                  if (mic.deviceId === 'default' || mic.deviceId === 'communications') {
                    deviceName = `${deviceName} (${mic.deviceId === 'default' ? 'Mặc định' : 'Liên lạc'})`;
                  }
                  if (!mic.label) {
                    deviceName += ` (ID: ${mic.deviceId.substring(0, 8)}...)`;
                  }

                  return (
                    <Select.Option key={mic.deviceId} value={mic.deviceId}>
                      {deviceName}
                    </Select.Option>
                  );
                })}
              </Select>

              {/* {availableMics.length > 0 && selectedMic && (
                <div style={{marginBottom: '10px', fontSize: '12px', color: '#666'}}>
                  <p>{t('SELECTED_MIC_INFO', 'Thông tin microphone đã chọn')}:</p>
                  <div style={{backgroundColor: '#f5f5f5', padding: '5px', borderRadius: '4px'}}>
                    {availableMics.find(m => m.deviceId === selectedMic)?.label || 'Không có thông tin label'}
                    <br />
                    ID: {selectedMic}
                  </div>
                </div>
              )} */}

              {availableMics.length === 0 && (
                <p style={{color: 'red', margin: '0px'}}>
                  {t(
                    'NO_MICS_FOUND',
                    'Không tìm thấy microphone nào. Bạn cần cấp quyền và có ít nhất một microphone để tiếp tục.',
                  )}
                </p>
              )}
            </div>

            <div className="role-play-session-screen__device-note">
              <p className='m-0'>
                <strong>
                  {t('NOTE_COACH', 'Lưu ý: ')}
                </strong>
                {t(
                  'DEVICE_SELECTION_NOTE',
                  'Microphone là bắt buộc để bắt đầu phiên. Bạn có thể tắt camera nếu muốn.',
                )}
              </p>
              <p className='m-0 mt-2'>
                {t(
                  'PERMISSION_NOTE',
                  'Nếu không thấy thiết bị, vui lòng kiểm tra quyền truy cập trong trình duyệt của bạn và làm mới danh sách.',
                )}
              </p>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default MediaDeviceSelectionModal;
